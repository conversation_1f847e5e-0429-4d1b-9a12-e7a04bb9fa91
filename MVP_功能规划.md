# ohMyPets MVP 产品功能规划（社区繁荣版）

（仅限产品功能规划；不含商业、技术实现与SLA等非功能内容）

## 1. 目标与范围

- **MVP导向**：先做“实用宠物应用 + 宠物生活内容记录”，将线下情感延续到线上，形成持续留存的社区土壤。
- **核心目标**：繁荣社区（提升优质内容供给与互动），为后续“话题讨论/交友扩展→服务整合”打基础。
- **覆盖人群**：新手/日常记录型宠物主、爱好者与轻创作者。

## 2. 分阶段功能包（3个迭代周期）

### 2.1 Phase 0.1 实用应用 + 内容记录（第1月）

- 实用应用 Lite：
  - 宠物档案（多宠）：头像、昵称、生日/年龄、性别、品种、性格标签。
  - 成长相册/日记：照片/短视频记录、里程碑（领回家、第一次洗澡等）卡片。
  - 基础提醒（可选开关）：喂养/驱虫/体重记录轻提醒（不涉及医疗与处方）。
  - 纪念日与时间轴：宠物周年、生日、成长瞬间时间线。
- 内容创作：
  - 发布图文/短视频、草稿箱、基础滤镜/裁剪、封面与标题模板。
  - 关联宠物、添加基础标签（城市/品种/场景），话题选择（基础通用话题，如#成长记录#、#萌宠日常#）。
- 内容消费（规则版）：
  - 推荐/关注/同城三栏 Feed（规则混排，首发不做复杂算法）。
  - 内容详情：点赞、评论、收藏、转发分享（友链/系统分享）。
- 账号与资料：
  - 注册登录（手机号/Apple/微信其一+短信验证），基础资料与隐私设置（作品可见范围）。
- 通知中心：
  - 点赞/评论/关注/系统通知。
  - 献花通知：收到献花/宠物献花榜上榜提醒。

### 2.2 Phase 0.2 互动增强 + 话题广场（第2月）

- 互动与关系：
  - 评论回复与@、楼中楼，关注推荐与分组（全部/宠物/达人）。
  - 稍后看/收藏夹管理、内容多图拼贴模板、定时发布。
  - 献花互动：对宠物主页与内容详情一键献花；每日免费配额；收花数公开展示。
- 话题体系：
  - 话题广场与话题页：官方话题位、热门/最新/同城排序。
  - 话题投稿与精选，基础话题规则与巡检（运营端配置）。
  - 新增领域话题入口（逐步开放）：领养、训练、饮食、运动等。
- 发现与榜单：
  - 新锐榜、品种榜、城市榜（规则榜单，按互动与新鲜度）。
  - 宠物献花榜（日/周）。
- 搜索与发现：
  - 搜索用户/宠物/话题/内容；推荐搜索词与热搜榜。
- 创作引导与任务：
  - 首帖引导、连续打卡（7/14天）、新手挑战与模版套用。

### 2.3 Phase 0.3 社区繁荣 + 基础治理（第3月）

- 圈层与同城：
  - 品种圈与同城圈页面，圈内热门、活动预告（报名意向，不含票务）。
- 轻社交：
  - 用户页留言板（替代即时私信），自定义问候语模版。
- 创作者成长（用户成长体系V0）：
  - 积分：发布、互动、连续打卡可获得成长积分（公开积分规则）。
  - 等级：Lv1-Lv5等级展示与进度条，等级名可读（如新手/活跃/达人）。
  - 徽章：新手/活跃/优质作者三类基础徽章。
  - 展示：个人主页显示等级与徽章墙。
- 宠物成长体系V0：
  - 成长阶段：幼年/成长期/成年/熟年（按品种与年龄段映射）。
  - 里程碑：内置里程碑模板（领回家/第一次洗澡等），支持手动添加与勾选达成。
  - 成长徽章：达成阶段与里程碑后生成成长徽章/证书卡片，可分享。
  - 展示：宠物主页显示成长阶段环、里程碑完成度与累计收花数。
- 安全与治理：
  - 举报/拉黑、敏感词库（基础）、作品编辑/删除与申诉入口。
  - 内容可见范围：公开/私密/仅互关。

## 3. 功能清单与优先级

### 3.1 Must-have（首发必须）

- 发布：图文/短视频、草稿箱、封面/标题模板、关联宠物与基础标签。
- 消费：推荐/关注/同城 Feed（规则）、内容详情（点赞/评论/收藏/分享）。
- 宠物：多宠档案、成长相册/日记、里程碑卡片、时间轴。
- 账号：注册登录、资料、隐私与黑名单、通知中心。
- 话题：基础话题选择与话题页（通用型），话题投稿。

### 3.2 Should-have（MVP窗口交付）

- 互动：评论@与回复、收藏夹管理、稍后看、献花互动（每日配额、公开收花）。
- 发现：新锐榜/品种榜/城市榜、宠物献花榜（日/周）；搜索（用户/宠物/话题/内容）。
- 玩法：首帖引导、连续打卡、主题模板（成长记录/日常）。
- 话题广场：官方位、热门/最新/同城排序、基础规则配置。
- 圈层：品种圈/同城圈聚合页。
- 成长体系V0：用户成长（积分/等级/徽章）、宠物成长（阶段/里程碑/成长徽章）。

### 3.3 Could-have（观察后择机）

- 创作：定时发布、多图拼贴、字幕与BGM轻量库。
- 轻社交：用户页留言板、好友推荐（规则版）。
- 成长年鉴：年度总结卡片（图文/短视频自动生成）。
- 小工具：证件照模板、纪念日挂件、养宠清单模板。
- 成长特效：用户等级特效、献花动效、宠物成长证书的动态展示。

## 4. 核心用户路径（功能视角）

- 新用户：
  - 登录→兴趣勾选（品种/城市）→推荐 Feed→关注作者/话题→首帖发布→给心仪宠物献花→收到互动与通知→持续记录。
- 记录型用户：
  - 创建宠物档案→成长相册/日记→关联话题与标签→加入品种圈/同城圈→连续打卡→解锁宠物成长徽章→参与榜单。
- 轻创作者：
  - 选题模板→拍摄/上传→封面/标题→话题投稿→评论互动与收花→获得用户成长积分/徽章与曝光。

## 5. 关键界面与信息架构（IA骨架）

- 底部导航：主页（推荐/关注/同城）｜话题｜创作｜消息｜我的。
- 我的：用户主页、宠物管理、多宠档案、草稿箱、收藏/稍后看、设置与隐私、成长等级与徽章墙。
- 话题：话题广场、话题页（精选/最新/同城）、投稿入口与规则。
- 内容详情：作品、作者卡片、关联宠物、点赞/评论/收藏/分享、举报/拉黑、献花入口。
- 宠物主页：头像与基本信息、成长阶段环/里程碑、累计收花数与献花按钮、宠物献花榜入口。

## 6. 非目标与延期项（MVP不纳入）

- 交易与服务：预约/支付/分账、商家入驻与保障、订单与理赔。
- 医疗在线咨询、复杂健康管理与处方能力。
- 开放平台（API/小程序/H5）、B端SaaS订阅、直播/合拍等复杂玩法。
- 复杂算法推荐（首期以规则混排为主）。

## 7. 功能性验收要点（示例）

- 发布：单次发布支持≥9图或1个短视频；可选宠物/话题/标签；支持草稿保存与再次编辑。
- Feed：三栏切换毫秒级无感刷新；下拉刷新、上拉分页；空态与失败重试。
- 互动：评论@与回复、删除自己的评论、举报不当内容、黑名单屏蔽；献花对宠物有效（主页与内容详情均可）、单用户对同一宠物每日上限（可配置）、献花成功回执与通知。
- 宠物：多宠维护、相册与日记可按时间轴浏览与搜索；里程碑模板可编辑；宠物主页展示成长阶段与累计收花数。
- 成长体系：
  - 用户成长：积分获取（发布/评论/被互动/连续打卡），等级展示（Lv1-Lv5）与徽章（新手/活跃/优质作者）达成与展示。
  - 宠物成长：阶段映射（按品种与年龄段）、里程碑达成记录、成长徽章/证书卡片生成与展示。
- 话题：支持话题投稿、话题页展示精选/最新/同城；话题规则说明可见。
- 通知：点赞/评论/关注/献花/系统通知可见并可设置免打扰。

---

附注：本版本仅面向“社区繁荣”的MVP功能建设，为后续“交友增强/专业话题深化/服务整合”预留扩展点（话题体系、圈层入口、创作者成长与治理能力）。
